from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, <PERSON><PERSON>an, Index
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime
import json

Base = declarative_base()

# 用户-聊天室关联表 (现在使用独立的模型类)
# user_chatroom = Table(
#     "user_chatroom",
#     Base.metadata,
#     Column("user_id", String(36), ForeignKey("users.id")),
#     Column("chatroom_id", Integer, ForeignKey("chatrooms.id"))
# )

class User(Base):
    __tablename__ = "users"

    id = Column(String(36), primary_key=True)
    username = Column(String(50), nullable=False)
    avatar = Column(String(255), nullable=True)

    # 关系
    messages = relationship("Message", back_populates="sender")
    # 通过UserChatroom关联表建立关系
    user_chatrooms = relationship("UserChatroom", back_populates="user")

class ChatRoom(Base):
    __tablename__ = "mf_chatroom"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    name = Column(String(100), nullable=True)  # 群聊名称，私聊为空
    is_group = Column(Boolean, default=False)  # 是否为群聊
    created_at = Column(DateTime, default=datetime.now)

    # 关系
    messages = relationship("Message", back_populates="chatroom", order_by="desc(Message.created_at)")
    # 通过UserChatroom关联表建立关系
    user_chatrooms = relationship("UserChatroom", back_populates="chatroom")
    task_chatrooms = relationship("TaskChatroom", back_populates="chatroom")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "is_group": self.is_group,
            "created_at": self.created_at.isoformat(),
            "members": [uc.user_id for uc in self.user_chatrooms],
            "last_message": self.messages[0].to_dict() if self.messages else None
        }

class Message(Base):
    __tablename__ = "mf_chat_messages"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_edited = Column(Boolean, default=False)
    is_html = Column(Boolean, default=False)  # 标记消息是否包含HTML
    message_type = Column(String(50), nullable=True)  # 消息类型，如"text", "image", "file", "call_signal"等
    content_type = Column(String(50), default="text")  # 内容类型，如"text", "image", "file"等

    # 外键
    sender_id = Column(String(36), ForeignKey("users.id"))
    chatroom_id = Column(Integer, ForeignKey("mf_chatroom.id"), default=0)

    # 关系
    sender = relationship("User", back_populates="messages")
    chatroom = relationship("ChatRoom", back_populates="messages")

    def to_dict(self):
        return {
            "id": self.id,
            "content": self.content,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "is_edited": self.is_edited,
            "is_html": self.is_html,  # 添加is_html字段
            "message_type": self.message_type,  # 添加message_type字段
            "content_type": self.content_type,  # 添加content_type字段
            "sender_id": self.sender_id,
            "chatroom_id": self.chatroom_id,
            "sender_name": self.sender.username if self.sender else None
        }


class CardConfirmation(Base):
    """卡片确认记录表"""
    __tablename__ = "card_confirmations"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    card_id = Column(String(100), nullable=False, comment="卡片ID")
    card_type = Column(String(50), nullable=False, comment="卡片类型")
    message_id = Column(Integer, ForeignKey("mf_chat_messages.id"), nullable=True, comment="关联的消息ID")
    chatroom_id = Column(Integer, ForeignKey("mf_chatroom.id"), nullable=False, comment="聊天室ID")
    confirmed_by = Column(String(36), ForeignKey("users.id"), nullable=False, comment="确认用户ID")
    confirmed_at = Column(DateTime, default=datetime.now, nullable=False, comment="确认时间")

    # 关系
    message = relationship("Message", backref="card_confirmations")
    chatroom = relationship("ChatRoom")
    user = relationship("User")

    # 索引
    __table_args__ = (
        Index('idx_card_confirmation_card', 'card_id', 'card_type'),
        Index('idx_card_confirmation_chatroom', 'chatroom_id'),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "card_id": self.card_id,
            "card_type": self.card_type,
            "message_id": self.message_id,
            "chatroom_id": self.chatroom_id,
            "confirmed_by": self.confirmed_by,
            "confirmed_at": self.confirmed_at.isoformat(),
            "user_name": self.user.username if self.user else None
        }


class UserChatroom(Base):
    """用户和房间对应表"""
    __tablename__ = "mf_user_chatroom"

    chatroom_id = Column(Integer, ForeignKey("mf_chatroom.id"), default=0, primary_key=True)
    user_id = Column(String(36), ForeignKey("users.id"), primary_key=True)
    user_type = Column(String(36), nullable=True, comment="用户角色 1雇主; 2设计师; 3管理员; 4普通成员")
    user_type_name = Column(String(36), nullable=True, comment="角色名称")
    user_name = Column(String(36), nullable=True, comment="用户名")

    # 关系
    user = relationship("User", back_populates="user_chatrooms")
    chatroom = relationship("ChatRoom", back_populates="user_chatrooms")

    # 索引
    __table_args__ = (
        Index('idx_user_chatroom_user', 'user_id'),
        Index('idx_user_chatroom_chatroom', 'chatroom_id'),
    )

    def to_dict(self):
        return {
            "chatroom_id": self.chatroom_id,
            "user_id": self.user_id,
            "user_type": self.user_type,
            "user_type_name": self.user_type_name,
            "user_name": self.user_name
        }


class TaskChatroom(Base):
    """任务和房间对应表"""
    __tablename__ = "mf_task_chatroom"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    task_id = Column(Integer, nullable=True, comment="任务id")
    chat_id = Column(Integer, ForeignKey("mf_chatroom.id"), nullable=True, comment="房间ID")
    status = Column(Integer, default=1, comment="群聊状态 （申请中1，已建立2，已拒绝3 ）")

    # 关系
    chatroom = relationship("ChatRoom", back_populates="task_chatrooms")

    def to_dict(self):
        return {
            "id": self.id,
            "task_id": self.task_id,
            "chat_id": self.chat_id,
            "status": self.status
        }