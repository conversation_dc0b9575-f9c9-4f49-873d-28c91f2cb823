from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from app.chat_manager.server import ConnectionManager
from pydantic import BaseModel, Field, validator
from utils.redis_util import get_redis
from app.database import get_db, generate_id
from app.models import User, ChatRoom, Message, UserChatroom
from app.utils.message_filter import MessageFilter
from app.utils.token_util import verify_token
from sqlalchemy.orm import Session
from sqlalchemy import select, func, desc
from typing import List, Optional
import json

app = APIRouter()

cm = ConnectionManager()


@app.websocket("/connect_chat")
async def connect_chat(websocket: WebSocket, user_code: str, db: Session = Depends(get_db)):
    try:
        await cm.connect(websocket, user_code, db)
    except WebSocketDisconnect:
        # 连接断开时移除连接
        await cm.disconnect(user_code)
    except Exception as e:
        # 处理其他异常
        print(f"WebSocket连接异常: {str(e)}")
        # 确保连接被清理
        await cm.disconnect(user_code)


@app.websocket("/connect_chat_with_token")
async def connect_chat_with_token(websocket: WebSocket, token: str, db: Session = Depends(get_db)):
    """使用令牌连接WebSocket"""
    try:
        # 验证令牌
        user_id = verify_token(token)
        if not user_id:
            await websocket.close(code=1008)  # 策略违规
            return

        # 连接WebSocket
        await cm.connect(websocket, user_id, db)
    except WebSocketDisconnect:
        await cm.disconnect(user_id)
    except Exception as e:
        print(f"WebSocket连接异常: {str(e)}")
        try:
            await cm.disconnect(user_id)
        except:
            pass


class ChatMessageModel(BaseModel):
    msg: str = Field(..., title="消息内容")
    sender: str = Field(..., title="发送者ID")
    recipient: Optional[str] = Field(None, title="接收者ID")
    chatroom_id: Optional[str] = Field(None, title="聊天室ID")

    @validator('msg')
    def validate_message_content(cls, v, values):
        # 检查消息是否包含手机号码
        # 如果有sender，就传入用户ID
        sender_id = values.get('sender') if values else None
        contains_phone, filtered_content, _ = MessageFilter.filter_phone_number(v, sender_id)
        if contains_phone:
            # 如果包含手机号码，返回过滤后的内容
            return filtered_content
        return v


@app.post("/send_message", summary="发送消息")
async def send_message(param: ChatMessageModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """发送消息到聊天室或私聊"""

    if param.chatroom_id:
        # 发送到聊天室
        ws_param = {
            "type": "group_message",
            "msg": param.msg,
            "sender": param.sender,
            "chatroom_id": param.chatroom_id
        }
    else:
        # 私聊消息
        ws_param = {
            "type": "private_message",
            "msg": param.msg,
            "sender": param.sender,
            "recipient": param.recipient
        }

    # 进行消息发布
    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


class EditMessageModel(BaseModel):
    message_id: str = Field(..., title="消息ID")
    content: str = Field(..., title="新消息内容")
    user_id: str = Field(..., title="用户ID")


@app.post("/edit_message", summary="编辑消息")
async def edit_message(param: EditMessageModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """编辑消息"""

    ws_param = {
        "type": "edit_message",
        "message_id": param.message_id,
        "content": param.content,
        "sender": param.user_id
    }

    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


class CreateGroupModel(BaseModel):
    name: str = Field(..., title="群聊名称")
    creator_id: str = Field(..., title="创建者ID")
    members: List[str] = Field(default=[], title="成员ID列表")


@app.post("/create_group", summary="创建群聊")
async def create_group(param: CreateGroupModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """创建群聊"""

    ws_param = {
        "type": "create_group",
        "name": param.name,
        "members": param.members,
        "sender": param.creator_id
    }

    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


@app.get("/chatrooms/{user_id}", summary="获取用户的聊天室列表")
async def get_chatrooms(user_id: str, db: Session = Depends(get_db)):
    """获取用户的聊天室列表"""

    # 查询用户
    user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 查询用户的聊天室 - 通过UserChatroom关联表
    stmt = select(ChatRoom).join(UserChatroom).filter(UserChatroom.user_id == user_id)
    chatrooms = db.execute(stmt).scalars().all()

    return {
        'code': 200,
        'msg': '成功',
        'data': [chatroom.to_dict() for chatroom in chatrooms]
    }


@app.get("/messages/{chatroom_id}", summary="获取聊天室的消息历史")
async def get_messages(chatroom_id: str, offset: int = 0, limit: int = 20, db: Session = Depends(get_db)):
    """获取聊天室的消息历史"""

    # 查询聊天室
    chatroom = db.execute(select(ChatRoom).filter(ChatRoom.id == chatroom_id)).scalar_one_or_none()
    if not chatroom:
        raise HTTPException(status_code=404, detail="聊天室不存在")

    # 查询消息
    stmt = select(Message).filter(
        Message.chatroom_id == chatroom_id
    ).order_by(desc(Message.created_at)).offset(offset).limit(limit)

    messages = db.execute(stmt).scalars().all()

    # 获取消息总数
    count_stmt = select(func.count(Message.id)).filter(Message.chatroom_id == chatroom_id)
    total_count = db.execute(count_stmt).scalar_one()

    return {
        'code': 200,
        'msg': '成功',
        'data': {
            'messages': [msg.to_dict() for msg in messages],
            'total': total_count,
            'offset': offset,
            'limit': limit
        }
    }


@app.get("/users/search", summary="搜索用户")
async def search_users(keyword: str, db: Session = Depends(get_db)):
    """搜索用户"""

    stmt = select(User).filter(User.username.like(f"%{keyword}%"))
    users = db.execute(stmt).scalars().all()

    return {
        'code': 200,
        'msg': '成功',
        'data': [
            {
                'id': user.id,
                'username': user.username,
                'avatar': user.avatar
            }
            for user in users
        ]
    }


@app.get("/connection/stats", summary="获取WebSocket连接状态")
async def get_connection_stats():
    """获取当前WebSocket连接状态的统计信息"""
    stats = cm.get_connection_stats()
    return {
        'code': 200,
        'msg': '成功',
        'data': stats
    }
