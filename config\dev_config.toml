[log]
log_path = "/data/log/python/old_biz/ws/runtime_{time:YYYY-MM}.log"
rotation = "1 month"
format = "{time:YYYY-MM-DD HH:mm:ss} |  {level}      | {name} : {function} : {line}  -  {message}"
retention = '60 days'


[db_redis]
host = "127.0.0.1"
port = 6379
db = 10
password = "123456"
timeout = 60000


[db_mysql]
host = "rm-bp1gsw85wvz6x8edc1o.mysql.rds.aliyuncs.com"
port = 3306
user = "mfcad_test"
psd = "Y8bR6A3t4zBzPgpJ"
database = "zb_mfcad_com"


[jwt]
secret_key = "your_secret_key_here"
algorithm = "HS256"
access_token_expire_minutes = 1440  # 24小时
